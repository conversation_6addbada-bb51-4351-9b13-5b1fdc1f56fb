using Godot;

public partial class RegionUnlockManager : Node
{
	public static RegionUnlockManager Instance { get; private set; }

	private bool _region2Unlocked = false;
	private bool _region3Unlocked = false;

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
			LoadUnlockStatus();
			ConnectSignals();
		}
		else
		{
			QueueFree();
		}
	}

	public override void _ExitTree()
	{
		DisconnectSignals();
	}

	private void LoadUnlockStatus()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData?.UnlockedRegions != null)
		{
			_region2Unlocked = resourcesManager.GameData.UnlockedRegions.Contains(2);
			_region3Unlocked = resourcesManager.GameData.UnlockedRegions.Contains(3);
		}
	}

	private void ConnectSignals()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.BridgeBuilt += OnBridgeBuilt;
			CommonSignals.Instance.RabbitLegCooked += OnRabbitLegCooked;
		}
	}

	private void DisconnectSignals()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.BridgeBuilt -= OnBridgeBuilt;
			CommonSignals.Instance.RabbitLegCooked -= OnRabbitLegCooked;
		}
	}

	private void OnBridgeBuilt()
	{
		if (!_region2Unlocked)
		{
			_region2Unlocked = true;
			UnlockRegion(2);
			GD.Print("RegionUnlockManager: Region 2 unlocked - first bridge built!");
		}
	}

	private void OnRabbitLegCooked()
	{
		if (!_region3Unlocked)
		{
			_region3Unlocked = true;
			UnlockRegion(3);
			GD.Print("RegionUnlockManager: Region 3 unlocked - rabbit leg cooked!");
		}
	}

	private void UnlockRegion(int regionId)
	{
		// Emit the region unlock signal
		CommonSignals.Instance?.EmitRegionUnlocked(regionId);
		
		// Save the unlock status
		SaveUnlockStatus(regionId);
	}

	private void SaveUnlockStatus(int regionId)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData != null)
		{
			if (resourcesManager.GameData.UnlockedRegions == null)
			{
				resourcesManager.GameData.UnlockedRegions = new System.Collections.Generic.List<int>();
			}

			if (!resourcesManager.GameData.UnlockedRegions.Contains(regionId))
			{
				resourcesManager.GameData.UnlockedRegions.Add(regionId);
				GD.Print($"RegionUnlockManager: Saved region {regionId} unlock status");
			}
		}
	}
}
