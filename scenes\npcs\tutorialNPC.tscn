[gd_scene load_steps=8 format=3 uid="uid://dd328ucgp21ei"]

[ext_resource type="Texture2D" uid="uid://d1xd3q7omqhh5" path="res://resources/Fantasy Dreamland/Characters Pack 1/Character_023.png" id="1_qh0t2"]
[ext_resource type="Script" uid="uid://ch4ta7gk3yofh" path="res://scenes/npcs/TutorialNPC.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://c6pcs5p57mb0m" path="res://resources/solaria/UI/dialogs/dialog1.png" id="2_qhmda"]
[ext_resource type="Texture2D" uid="uid://coohtgqediyob" path="res://resources/solaria/UI/dialogs/dialogGreenArrow.png" id="3_806w0"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="4_806w0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_5evfb"]
size = Vector2(8, 4)

[sub_resource type="CircleShape2D" id="CircleShape2D_806w0"]
radius = 13.0

[node name="TutorialNpc" type="Sprite2D"]
y_sort_enabled = true
texture = ExtResource("1_qh0t2")
hframes = 4
vframes = 4
frame = 1
script = ExtResource("1_script")

[node name="StaticBody2d" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2d"]
y_sort_enabled = true
position = Vector2(0, 10)
shape = SubResource("RectangleShape2D_5evfb")

[node name="Dialog" type="Node2D" parent="."]

[node name="DialogPanel" type="Sprite2D" parent="Dialog"]
position = Vector2(-34, -20)
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_qhmda")

[node name="ContinueMark" type="Sprite2D" parent="Dialog"]
position = Vector2(-34, -13)
scale = Vector2(0.5, 0.5)
texture = ExtResource("3_806w0")

[node name="Label" parent="Dialog" instance=ExtResource("4_806w0")]
offset_left = -74.0
offset_top = -40.0
offset_right = 121.0
offset_bottom = 21.0
scale = Vector2(0.41, 0.41)
text = "Hello friend... this is a test message to see how the text will be distributed. "

[node name="DialogContinueButton" type="Button" parent="Dialog"]
offset_left = -74.0
offset_top = -40.0
offset_right = 6.0
offset_bottom = -9.0

[node name="PlayerDetectionArea2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetectionArea2D"]
position = Vector2(0, 3)
shape = SubResource("CircleShape2D_806w0")
