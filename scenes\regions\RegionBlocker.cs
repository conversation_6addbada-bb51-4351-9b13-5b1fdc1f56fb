using Godot;
using System.Collections.Generic;

public partial class RegionBlocker : Sprite2D
{
	[Export] public int RegionId { get; set; } = 2;
	
	private bool _isUnlocked = false;
	private Tween _fadeTween;
	private StaticBody2D _staticBody;

	public override void _Ready()
	{
		_staticBody = GetNode<StaticBody2D>("StaticBody2D");
		
		LoadRegionStatus();
		
		if (_isUnlocked)
		{
			QueueFree();
			return;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked -= OnRegionUnlocked;
		}
	}

	private void LoadRegionStatus()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData?.UnlockedRegions != null &&
			resourcesManager.GameData.UnlockedRegions.Contains(RegionId))
		{
			_isUnlocked = true;
		}
	}

	private void SaveRegionStatus()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData != null)
		{
			if (resourcesManager.GameData.UnlockedRegions == null)
			{
				resourcesManager.GameData.UnlockedRegions = new List<int> { 1 };
			}

			if (!resourcesManager.GameData.UnlockedRegions.Contains(RegionId))
			{
				resourcesManager.GameData.UnlockedRegions.Add(RegionId);
				resourcesManager.ForceSave();
			}
		}
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == RegionId && !_isUnlocked)
		{
			_isUnlocked = true;
			SaveRegionStatus();
			StartFadeOut();
		}
	}

	private void StartFadeOut()
	{
		_fadeTween?.Kill();
		_fadeTween = CreateTween();
		
		_fadeTween.TweenProperty(this, "modulate:a", 0.0f, 2.0f);
		_fadeTween.TweenCallback(Callable.From(QueueFree));
	}

	public void UnlockRegion()
	{
		if (!_isUnlocked)
		{
			CommonSignals.Instance?.EmitRegionUnlocked(RegionId);
		}
	}
}
