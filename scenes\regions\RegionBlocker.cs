using Godot;
using System.Collections.Generic;

public partial class RegionBlocker : Sprite2D
{
	[Export] public int RegionId { get; set; } = 2;
	
	private bool _isUnlocked = false;
	private Tween _fadeTween;
	private StaticBody2D _staticBody;

	public override void _Ready()
	{
		_staticBody = GetNode<StaticBody2D>("StaticBody2D");
		
		LoadRegionStatus();
		
		if (_isUnlocked)
		{
			QueueFree();
			return;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked -= OnRegionUnlocked;
		}
	}

	private void LoadRegionStatus()
	{
		var gameData = SaveHandler.Load<GameData>("gamedata");
		if (gameData?.UnlockedRegions != null && gameData.UnlockedRegions.Contains(RegionId))
		{
			_isUnlocked = true;
		}
	}

	private void SaveRegionStatus()
	{
		var gameData = SaveHandler.Load<GameData>("gamedata") ?? new GameData();
		
		if (gameData.UnlockedRegions == null)
		{
			gameData.UnlockedRegions = new List<int>();
		}

		if (!gameData.UnlockedRegions.Contains(RegionId))
		{
			gameData.UnlockedRegions.Add(RegionId);
			SaveHandler.Save(gameData, "gamedata");
		}
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == RegionId && !_isUnlocked)
		{
			_isUnlocked = true;
			SaveRegionStatus();
			StartFadeOut();
		}
	}

	private void StartFadeOut()
	{
		_fadeTween?.Kill();
		_fadeTween = CreateTween();
		
		_fadeTween.TweenProperty(this, "modulate:a", 0.0f, 2.0f);
		_fadeTween.TweenCallback(Callable.From(QueueFree));
	}

	public void UnlockRegion()
	{
		if (!_isUnlocked)
		{
			CommonSignals.Instance?.EmitRegionUnlocked(RegionId);
		}
	}
}
