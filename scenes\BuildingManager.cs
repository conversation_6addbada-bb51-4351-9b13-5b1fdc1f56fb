using Godot;
using System.Collections.Generic;

public partial class BuildingManager : Node2D
{
	[Export] public PackedScene AnvilScene { get; set; }
	[Export] public PackedScene BridgeScene { get; set; }
	[Export] public PackedScene CampfireScene { get; set; }

	private Dictionary<string, Node2D> _activeBuildings = new();

	public override void _Ready()
	{
		if (AnvilScene == null)
		{
			AnvilScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Anvil.tscn");
		}

		if (BridgeScene == null)
		{
			BridgeScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Bridge.tscn");
		}

		if (CampfireScene == null)
		{
			CampfireScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Campfire.tscn");
		}

		CallDeferred(nameof(LoadExistingBuildings));
	}

	private void LoadExistingBuildings()
	{
		var buildings = ResourcesManager.Instance?.GetBuildings();
		if (buildings == null || buildings.Count == 0)
		{
			return;
		}

		foreach (var buildingData in buildings)
		{
			LoadBuilding(buildingData);
		}
	}

	private bool LoadBuilding(BuildingData buildingData)
	{
		if (string.IsNullOrEmpty(buildingData.BuildingType))
		{
			return false;
		}

		if (_activeBuildings.ContainsKey(buildingData.Id))
		{
			return false;
		}

		Node2D buildingInstance = null;

		switch (buildingData.BuildingType)
		{
			case "Anvil":
				buildingInstance = LoadAnvil(buildingData);
				break;
			case "Bridge":
				buildingInstance = LoadBridge(buildingData);
				break;
			case "Campfire":
				buildingInstance = LoadCampfire(buildingData);
				break;
			default:
				return false;
		}

		if (buildingInstance != null)
		{
			_activeBuildings[buildingData.Id] = buildingInstance;
			return true;
		}

		return false;
	}

	private Anvil LoadAnvil(BuildingData buildingData)
	{
		if (AnvilScene == null)
		{
			return null;
		}

		var anvil = AnvilScene.Instantiate<Anvil>();
		if (anvil == null)
		{
			return null;
		}

		anvil.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", anvil);
		}
		else
		{
			GetParent().CallDeferred("add_child", anvil);
		}

		return anvil;
	}

	private Bridge LoadBridge(BuildingData buildingData)
	{
		if (BridgeScene == null)
		{
			return null;
		}

		var bridge = BridgeScene.Instantiate<Bridge>();
		if (bridge == null)
		{
			return null;
		}

		bridge.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", bridge);
		}
		else
		{
			GetParent().CallDeferred("add_child", bridge);
		}

		return bridge;
	}

	private Campfire LoadCampfire(BuildingData buildingData)
	{
		if (CampfireScene == null)
		{
			return null;
		}

		var campfire = CampfireScene.Instantiate<Campfire>();
		if (campfire == null)
		{
			return null;
		}

		campfire.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", campfire);
		}
		else
		{
			GetParent().CallDeferred("add_child", campfire);
		}

		return campfire;
	}

	public bool RemoveBuilding(string buildingId)
	{
		if (_activeBuildings.TryGetValue(buildingId, out var building))
		{
			building.QueueFree();
			_activeBuildings.Remove(buildingId);
			return true;
		}
		return false;
	}

	public Dictionary<string, Node2D> GetAllBuildings()
	{
		return new Dictionary<string, Node2D>(_activeBuildings);
	}

	public void ClearAllBuildings()
	{
		foreach (var building in _activeBuildings.Values)
		{
			building.QueueFree();
		}
		_activeBuildings.Clear();

		ResourcesManager.Instance?.ClearBuildings();
	}
}
