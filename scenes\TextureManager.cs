using Godot;

/// <summary>
/// Manages textures for all resource types in the game
/// This class should be added to AutoLoad in project settings
/// </summary>
public partial class TextureManager : Node
{
	// Singleton instance
	public static TextureManager Instance { get; private set; }

	// Resource textures (for UI, inventory, etc.) - set these in the editor
	[Export] public Texture2D WoodTexture { get; set; }
	[Export] public Texture2D StoneTexture { get; set; }
	[Export] public Texture2D NetTexture { get; set; }
	[Export] public Texture2D PlankTexture { get; set; }
	[Export] public Texture2D Stone2Texture { get; set; }
	[Export] public Texture2D BerryTexture { get; set; }
	[Export] public Texture2D LeafTexture { get; set; }

	[Export] public Texture2D CopperOreTexture { get; set; }
	[Export] public Texture2D IronOreTexture { get; set; }
	[Export] public Texture2D GoldOreTexture { get; set; }
	[Export] public Texture2D IndigosiumOreTexture { get; set; }
	[Export] public Texture2D MithrilOreTexture { get; set; }
	[Export] public Texture2D ErithrydiumOreTexture { get; set; }
	[Export] public Texture2D AdamantiteOreTexture { get; set; }
	[Export] public Texture2D UraniumOreTexture { get; set; }

	[Export] public Texture2D CopperBarTexture { get; set; }
	[Export] public Texture2D IronBarTexture { get; set; }
	[Export] public Texture2D GoldBarTexture { get; set; }
	[Export] public Texture2D IndigosiumBarTexture { get; set; }
	[Export] public Texture2D MithrilBarTexture { get; set; }
	[Export] public Texture2D ErithrydiumBarTexture { get; set; }
	[Export] public Texture2D AdamantiteBarTexture { get; set; }
	[Export] public Texture2D UraniumBarTexture { get; set; }

	[Export] public Texture2D CopperSheetTexture { get; set; }
	[Export] public Texture2D IronSheetTexture { get; set; }
	[Export] public Texture2D GoldSheetTexture { get; set; }
	[Export] public Texture2D IndigosiumSheetTexture { get; set; }
	[Export] public Texture2D MithrilSheetTexture { get; set; }
	[Export] public Texture2D ErithrydiumSheetTexture { get; set; }
	[Export] public Texture2D AdamantiteSheetTexture { get; set; }
	[Export] public Texture2D UraniumSheetTexture { get; set; }

	[Export] public Texture2D WoodenBeamTexture { get; set; }
	[Export] public Texture2D WoodenStickTexture { get; set; }
	[Export] public Texture2D RawRabbitLegTexture { get; set; }
	[Export] public Texture2D CookedRabbitLegTexture { get; set; }

	// Resource icon textures (for dropped items, small displays) - set these in the editor
	[Export] public Texture2D WoodIconTexture { get; set; }
	[Export] public Texture2D StoneIconTexture { get; set; }
	[Export] public Texture2D NetIconTexture { get; set; }
	[Export] public Texture2D PlankIconTexture { get; set; }
	[Export] public Texture2D Stone2IconTexture { get; set; }
	[Export] public Texture2D BerryIconTexture { get; set; }
	[Export] public Texture2D LeafIconTexture { get; set; }

	[Export] public Texture2D CopperOreIconTexture { get; set; }
	[Export] public Texture2D IronOreIconTexture { get; set; }
	[Export] public Texture2D GoldOreIconTexture { get; set; }
	[Export] public Texture2D IndigosiumOreIconTexture { get; set; }
	[Export] public Texture2D MithrilOreIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumOreIconTexture { get; set; }
	[Export] public Texture2D AdamantiteOreIconTexture { get; set; }
	[Export] public Texture2D UraniumOreIconTexture { get; set; }

	[Export] public Texture2D CopperBarIconTexture { get; set; }
	[Export] public Texture2D IronBarIconTexture { get; set; }
	[Export] public Texture2D GoldBarIconTexture { get; set; }
	[Export] public Texture2D IndigosiumBarIconTexture { get; set; }
	[Export] public Texture2D MithrilBarIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumBarIconTexture { get; set; }
	[Export] public Texture2D AdamantiteBarIconTexture { get; set; }
	[Export] public Texture2D UraniumBarIconTexture { get; set; }

	[Export] public Texture2D CopperSheetIconTexture { get; set; }
	[Export] public Texture2D IronSheetIconTexture { get; set; }
	[Export] public Texture2D GoldSheetIconTexture { get; set; }
	[Export] public Texture2D IndigosiumSheetIconTexture { get; set; }
	[Export] public Texture2D MithrilSheetIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumSheetIconTexture { get; set; }
	[Export] public Texture2D AdamantiteSheetIconTexture { get; set; }
	[Export] public Texture2D UraniumSheetIconTexture { get; set; }

	[Export] public Texture2D WoodenBeamIconTexture { get; set; }
	[Export] public Texture2D WoodenStickIconTexture { get; set; }
	[Export] public Texture2D RawRabbitLegIconTexture { get; set; }
	[Export] public Texture2D CookedRabbitLegIconTexture { get; set; }

	// Tool textures (for UI panels) - set these in the editor
	[Export] public Texture2D PickaxeTexture { get; set; }
	[Export] public Texture2D HammerTexture { get; set; }
	[Export] public Texture2D WateringCanTexture { get; set; }
	[Export] public Texture2D HoeTexture { get; set; }
	[Export] public Texture2D SwordTexture { get; set; }
	[Export] public Texture2D BowTexture { get; set; }

	public override void _Ready()
	{
		// Set singleton instance
		if (Instance == null)
		{
			Instance = this;
			GD.Print("TextureManager initialized and ready");
		}
		else
		{
			// Prevent duplicate instances
			QueueFree();
		}
	}

	/// <summary>
	/// Get texture for a specific resource type (for UI, inventory, etc.)
	/// </summary>
	/// <param name="resourceType">The resource type to get texture for</param>
	/// <returns>Texture2D for the resource, or null if not found</returns>
	public Texture2D GetResourceTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodTexture,
			ResourceType.Stone => StoneTexture,
			ResourceType.Net => NetTexture,
			ResourceType.Plank => PlankTexture,
			ResourceType.Stone2 => Stone2Texture,
			ResourceType.Berry => BerryTexture,
			ResourceType.Leaf => LeafTexture,
			ResourceType.CopperOre => CopperOreTexture,
			ResourceType.IronOre => IronOreTexture,
			ResourceType.GoldOre => GoldOreTexture,
			ResourceType.IndigosiumOre => IndigosiumOreTexture,
			ResourceType.MithrilOre => MithrilOreTexture,
			ResourceType.ErithrydiumOre => ErithrydiumOreTexture,
			ResourceType.AdamantiteOre => AdamantiteOreTexture,
			ResourceType.UraniumOre => UraniumOreTexture,
			ResourceType.CopperBar => CopperBarTexture,
			ResourceType.IronBar => IronBarTexture,
			ResourceType.GoldBar => GoldBarTexture,
			ResourceType.IndigosiumBar => IndigosiumBarTexture,
			ResourceType.MithrilBar => MithrilBarTexture,
			ResourceType.ErithrydiumBar => ErithrydiumBarTexture,
			ResourceType.AdamantiteBar => AdamantiteBarTexture,
			ResourceType.UraniumBar => UraniumBarTexture,
			ResourceType.CopperSheet => CopperSheetTexture,
			ResourceType.IronSheet => IronSheetTexture,
			ResourceType.GoldSheet => GoldSheetTexture,
			ResourceType.IndigosiumSheet => IndigosiumSheetTexture,
			ResourceType.MithrilSheet => MithrilSheetTexture,
			ResourceType.ErithrydiumSheet => ErithrydiumSheetTexture,
			ResourceType.AdamantiteSheet => AdamantiteSheetTexture,
			ResourceType.UraniumSheet => UraniumSheetTexture,
			ResourceType.WoodenBeam => WoodenBeamTexture,
			ResourceType.WoodenStick => WoodenStickTexture,
			ResourceType.RawRabbitLeg => RawRabbitLegTexture,
			ResourceType.CookedRabbitLeg => CookedRabbitLegTexture,
			_ => null
		};
	}

	/// <summary>
	/// Get icon texture for a specific resource type (for dropped items, small displays)
	/// </summary>
	/// <param name="resourceType">The resource type to get icon texture for</param>
	/// <returns>Texture2D for the resource icon, or null if not found</returns>
	public Texture2D GetResourceIconTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodIconTexture,
			ResourceType.Stone => StoneIconTexture,
			ResourceType.Net => NetIconTexture,
			ResourceType.Plank => PlankIconTexture,
			ResourceType.Stone2 => Stone2IconTexture,
			ResourceType.Berry => BerryIconTexture,
			ResourceType.Leaf => LeafIconTexture,
			ResourceType.CopperOre => CopperOreIconTexture,
			ResourceType.IronOre => IronOreIconTexture,
			ResourceType.GoldOre => GoldOreIconTexture,
			ResourceType.IndigosiumOre => IndigosiumOreIconTexture,
			ResourceType.MithrilOre => MithrilOreIconTexture,
			ResourceType.ErithrydiumOre => ErithrydiumOreIconTexture,
			ResourceType.AdamantiteOre => AdamantiteOreIconTexture,
			ResourceType.UraniumOre => UraniumOreIconTexture,
			ResourceType.CopperBar => CopperBarIconTexture,
			ResourceType.IronBar => IronBarIconTexture,
			ResourceType.GoldBar => GoldBarIconTexture,
			ResourceType.IndigosiumBar => IndigosiumBarIconTexture,
			ResourceType.MithrilBar => MithrilBarIconTexture,
			ResourceType.ErithrydiumBar => ErithrydiumBarIconTexture,
			ResourceType.AdamantiteBar => AdamantiteBarIconTexture,
			ResourceType.UraniumBar => UraniumBarIconTexture,
			ResourceType.CopperSheet => CopperSheetIconTexture,
			ResourceType.IronSheet => IronSheetIconTexture,
			ResourceType.GoldSheet => GoldSheetIconTexture,
			ResourceType.IndigosiumSheet => IndigosiumSheetIconTexture,
			ResourceType.MithrilSheet => MithrilSheetIconTexture,
			ResourceType.ErithrydiumSheet => ErithrydiumSheetIconTexture,
			ResourceType.AdamantiteSheet => AdamantiteSheetIconTexture,
			ResourceType.UraniumSheet => UraniumSheetIconTexture,
			ResourceType.WoodenBeam => WoodenBeamIconTexture,
			ResourceType.WoodenStick => WoodenStickIconTexture,
			ResourceType.RawRabbitLeg => RawRabbitLegIconTexture,
			ResourceType.CookedRabbitLeg => CookedRabbitLegIconTexture,
			_ => null
		};
	}

	/// <summary>
	/// Check if a texture is available for the given resource type
	/// </summary>
	/// <param name="resourceType">The resource type to check</param>
	/// <returns>True if texture is available, false otherwise</returns>
	public bool HasTexture(ResourceType resourceType)
	{
		return GetResourceTexture(resourceType) != null;
	}

	/// <summary>
	/// Check if an icon texture is available for the given resource type
	/// </summary>
	/// <param name="resourceType">The resource type to check</param>
	/// <returns>True if icon texture is available, false otherwise</returns>
	public bool HasIconTexture(ResourceType resourceType)
	{
		return GetResourceIconTexture(resourceType) != null;
	}

	/// <summary>
	/// Get texture for a specific tool type
	/// </summary>
	/// <param name="toolType">The tool type to get texture for</param>
	/// <returns>Texture2D for the tool, or null if not found</returns>
	public Texture2D GetToolTexture(ToolType toolType)
	{
		return toolType switch
		{
			ToolType.Pickaxe => PickaxeTexture,
			ToolType.Hammer => HammerTexture,
			ToolType.Hoe => HoeTexture,
			ToolType.WateringCan => WateringCanTexture,
			ToolType.Sword => SwordTexture,
			ToolType.Bow => BowTexture,
			_ => null
		};
	}

	/// <summary>
	/// Check if a texture is available for the given tool type
	/// </summary>
	/// <param name="toolType">The tool type to check</param>
	/// <returns>True if texture is available, false otherwise</returns>
	public bool HasToolTexture(ToolType toolType)
	{
		return GetToolTexture(toolType) != null;
	}

	/// <summary>
	/// Get all available resource types that have textures
	/// </summary>
	/// <returns>Array of resource types with available textures</returns>
	public ResourceType[] GetAvailableResourceTypes()
	{
		var availableTypes = new System.Collections.Generic.List<ResourceType>();

		foreach (ResourceType resourceType in System.Enum.GetValues<ResourceType>())
		{
			if (HasTexture(resourceType))
			{
				availableTypes.Add(resourceType);
			}
		}

		return availableTypes.ToArray();
	}
}
