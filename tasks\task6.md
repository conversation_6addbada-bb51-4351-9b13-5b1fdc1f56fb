your tasks:
1. in world.tscn i added node RegionBlockers. There will be a lot of regions, currently Region2,3,4. I want you to add logic: on ready() verify if region of given id is unlocked (in this case 2,3,4) - if yes then queue free given region blocker. also, if region is unlocked during gameplay i want this region to (which is sprite2d) to slowly became transparent and then queuefree. we need to load/save region unlock status when game is saved/started (autosave system). attach this script to Region2 Region3 Region4 and i will need to set region id in inspector. place script in scenes/regions directory.
2. Look at anvil - it has a nice animation when it gets hit. add similar to the bridge, also make sure that health bar is visible when hp of bridge is not at max level.
4. Add a campfire where player can craft food. I already added a scene and menu panel which is similar to the one - anvil and anvil panel. New scenes are Campfire.tscn and CampfireMenu.tscn. Player can build campfire from BuildMenu - i added ItemListCampfire where you can find it - implement like anvil building. The cost is 5 wood and 2 stone. Campfire is a bit different than anvil - it has a fire animation that should be played when campfire is built. progress bar should be filled when player starts createing food on it. the differenct to anvil is that it prepares food on its own and player doesnt need to use hammer - progress bar should update automatically. add dictionary of resource type and time to prepare in seconds. also add a dicrionary with prices - first item is rabbit leg. it needs 1 RawRabbitLeg (add this resource type texture manager) and 1 Wood and it takes 10 seconds to prepare and player gains 1 CookedRabbitLeg (add this resource type and update texture manager). when item is done then it should be dropped (icon) similar way like player cut a tree or a rock and then item is spawned and dropped - but here we will spawn rabbit leg (and later other food). on ready - play animation 'AnimateCampfire' that plays campfire animation .now, i changed menu panel - so in control->panel->scrollContainer->list items have 'Button' and when clicked then we should set properties of InfoBoard node to following: ItemFront - this should be sprite of selected resource, AmountToProduce label - this should be initially 1 when player selects different resource and can be increased by 1 when player clicks ButtonPlusOne button and decreased by ond when player clicks ButtonMinusOne button. min value is 0, max 999. when player clicks ButtonSetOne button then this value should be 1, when ButtonSet25Percent - 25% of items that player can afford, when ButtonSet50Percent - 50% of items that player can afford, when, when ButtonSetMax - max amount of items that player can afford. when player clicks ButtonProduce then we should check if player has enough resources to produce selected amount of items. if not, then basically ProduceButton should be a bit transparent (grayed out). if player can afford then start producing. there is also an animation player - handle like anvil to show and close panel.

3. Let's create an npc for tutorial purposes. I created scene tutorialNpc.tscn. root node is character sprite. it also has staticbody2d for collider purposes. it also has area2d to detect when player's PlayerDetector is in range. when player is in range then can click R to start talking. then NPC should tell specific dialog, depending on current state. npc story and quests are linear. the order has to be like this:
* player initially comes to npc and the npc says something like "Hello friend! My name is Nicko..." (here player needs to click on dialog continue button to continue. when presenting dialog texts you need to add one letter at a time, adding a letter every 0.1s, so that it looks like text is being typed. also there is a sprite with green arrow that should be presented when npc stops talking and player can go to next part of the dialog. this sprite is called ContinueMark in our scene).
* then npc says "I've got some quests for you" "Don't worry that won't take a lot of time" "I want you to get to know our new land quickly" "The most important thing is to eat and drink otherwise you will die" - you can refactor these texts so that it looks better, but keep the meaning. after player clicks continue button then next text appears, and so on. when all texts are presented then tell "Ok, first of all you will need an anvil to craft items" "You can find build menu at the bottom of the screen or by pressing B key" then tell "Come back after you build an anvil" - then player should build anvil. close dialog after player clicks to continue. if player will try to talk to npc again then tell "Come back after you build an anvil". Keep these texts small as we don't have much space on the screen. Texts should be taken from translations.csv file. Additionally, when player doesnt continue dialog for 30s then close dialog automatically and remember current step.
* when player builds anvil then npc should tell "Great! You have built an anvil" "Now you need to expand" "You can craft a bridge to cross the water" "Come back when you build a bridge and place it on the water". Repeat last text when player starts dialog again. 
in order to verify if player builds an anvil and bridge we need to add a signal(s) to common signals and listen to it in npc. When player builds a bridge then npc should tell "Great! You have built a bridge" "Now you can explore the new land" "Look! There's a land at east" (at this stage you should unlock region2, so that it disappesars and queuefree) "You can find more resources there" "Come back when you explore the new land".
* when player comes again: "Great! You have explored the new land" "Now let's learn how to hunt" "Come back after you hunt 2 rabbits" (repeat this text when player talks to npc again before hinting 2 rabbits) - we need to add a signal in common signals when rabbit dies so that npc knows that rabbit is hunted. when player talks to npc again then tell if player hunted 2 rabbits: "Great! You have hunted 2 rabbits" "Now you can cook them" "Let's build a campfire" -> player needs to build campfire and npc listen to event that campfire is built. When player comes to npc after building campfire then tell "Great! You have built a campfire" "Now you can cook the rabbits" "Come back when you cook the rabbit legs" -> again event and when player comes again then  tell "Good job, you have cooked the rabbit legs" "You can eate them to resotre health and hunger" "Look! There's a new land at south"  (at this stage you should unlock region3, so that it disappesars and queuefree) "Now you know basics to survive". Then npc should disappear. I will add this npc to world scene.

7. update technical.md
