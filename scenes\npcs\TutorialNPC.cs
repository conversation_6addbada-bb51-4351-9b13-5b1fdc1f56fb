using Godot;
using System.Collections.Generic;

public partial class TutorialNPC : Sprite2D
{
	private enum QuestState
	{
		Welcome,
		BuildAnvil,
		BuildBridge,
		HuntRabbit,
		BuildCampfire,
		CookMeat,
		Complete
	}

	private QuestState _currentQuest = QuestState.Welcome;
	private bool _isPlayerInRange = false;
	private bool _isDialogOpen = false;
	private int _currentDialogIndex = 0;

	// Dialog components
	private Node2D _dialogNode;
	private Sprite2D _dialogPanel;
	private Sprite2D _continueMark;
	private Label _dialogLabel;
	private Button _dialogContinueButton;
	private Area2D _playerDetectionArea;

	// Dialog texts for each quest state
	private readonly Dictionary<QuestState, List<string>> _questDialogs = new()
	{
		{
			QuestState.Welcome,
			new List<string>
			{
				"Welcome, traveler! I am here to guide you through the basics of survival.",
				"First, you'll need to build an Anvil to craft tools and materials.",
				"Gather 5 Wood and 5 Stone, then press 'B' to open the build menu.",
				"Build the Anvil and come back to me!"
			}
		},
		{
			QuestState.BuildAnvil,
			new List<string>
			{
				"Excellent work! The Anvil will help you craft better materials.",
				"Next, you need to build a Bridge to cross water.",
				"Use your Anvil to craft 5 Planks and 2 Wooden Beams first.",
				"Then build the Bridge and return to me!"
			}
		},
		{
			QuestState.BuildBridge,
			new List<string>
			{
				"Well done! Bridges allow you to explore new areas.",
				"Now you need to learn hunting for food.",
				"Find a rabbit and hunt it using your sword or bow.",
				"Come back once you've successfully hunted a rabbit!"
			}
		},
		{
			QuestState.HuntRabbit,
			new List<string>
			{
				"Great hunting! Raw meat won't last long though.",
				"You need to build a Campfire to cook your food.",
				"Gather 5 Wood and 2 Stone, then build a Campfire.",
				"Return to me once it's built!"
			}
		},
		{
			QuestState.BuildCampfire,
			new List<string>
			{
				"Perfect! Now you can cook your raw meat.",
				"Use the Campfire to cook Raw Rabbit Leg into Cooked Rabbit Leg.",
				"Press 'R' near the Campfire to open the cooking menu.",
				"Cook some meat and come back to complete your training!"
			}
		},
		{
			QuestState.CookMeat,
			new List<string>
			{
				"Congratulations! You've mastered the basics of survival.",
				"You can now build, hunt, and cook to sustain yourself.",
				"The world is yours to explore. Good luck, adventurer!"
			}
		},
		{
			QuestState.Complete,
			new List<string>
			{
				"You've completed all the basic training!",
				"Feel free to explore and build your settlement."
			}
		}
	};

	public override void _Ready()
	{
		_dialogNode = GetNode<Node2D>("Dialog");
		_dialogPanel = GetNode<Sprite2D>("Dialog/DialogPanel");
		_continueMark = GetNode<Sprite2D>("Dialog/ContinueMark");
		_dialogLabel = GetNode<Label>("Dialog/Label");
		_dialogContinueButton = GetNode<Button>("Dialog/DialogContinueButton");
		_playerDetectionArea = GetNode<Area2D>("PlayerDetectionArea2D");

		if (_playerDetectionArea != null)
		{
			// Set collision mask to detect PlayerDetector (layer 3)
			_playerDetectionArea.CollisionMask = 4; // Detect layer 3 (bit 2 = 4)
			_playerDetectionArea.AreaEntered += OnPlayerEntered;
			_playerDetectionArea.AreaExited += OnPlayerExited;
		}

		if (_dialogContinueButton != null)
		{
			_dialogContinueButton.Pressed += OnDialogContinuePressed;
		}

		// Connect to building and hunting signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.AnvilBuilt += OnAnvilBuilt;
			CommonSignals.Instance.BridgeBuilt += OnBridgeBuilt;
			CommonSignals.Instance.RabbitHunted += OnRabbitHunted;
			CommonSignals.Instance.CampfireBuilt += OnCampfireBuilt;
		}

		LoadQuestProgress();
		HideDialog();
	}

	public override void _ExitTree()
	{
		if (_playerDetectionArea != null)
		{
			_playerDetectionArea.AreaEntered -= OnPlayerEntered;
			_playerDetectionArea.AreaExited -= OnPlayerExited;
		}

		if (_dialogContinueButton != null)
		{
			_dialogContinueButton.Pressed -= OnDialogContinuePressed;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.AnvilBuilt -= OnAnvilBuilt;
			CommonSignals.Instance.BridgeBuilt -= OnBridgeBuilt;
			CommonSignals.Instance.RabbitHunted -= OnRabbitHunted;
			CommonSignals.Instance.CampfireBuilt -= OnCampfireBuilt;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || _isDialogOpen) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				StartDialog();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		_isPlayerInRange = true;
		GD.Print("TutorialNPC: Player in range - press 'R' to talk");
	}

	private void OnPlayerExited(Area2D area)
	{
		_isPlayerInRange = false;
		if (_isDialogOpen)
		{
			HideDialog();
		}
	}

	private void StartDialog()
	{
		if (!_questDialogs.TryGetValue(_currentQuest, out var dialogs) || dialogs.Count == 0)
			return;

		_isDialogOpen = true;
		_currentDialogIndex = 0;
		ShowDialog();
		UpdateDialogText();

		// Disable player movement during dialog
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);
	}

	private void OnDialogContinuePressed()
	{
		if (!_questDialogs.TryGetValue(_currentQuest, out var dialogs))
			return;

		_currentDialogIndex++;

		if (_currentDialogIndex >= dialogs.Count)
		{
			HideDialog();

			// Advance quest state after welcome dialog
			if (_currentQuest == QuestState.Welcome)
			{
				_currentQuest = QuestState.BuildAnvil;
				SaveQuestProgress();
				GD.Print("TutorialNPC: Welcome dialog completed, now build anvil!");
			}
		}
		else
		{
			UpdateDialogText();
		}
	}

	private void ShowDialog()
	{
		if (_dialogNode != null)
		{
			_dialogNode.Visible = true;
		}
	}

	private void HideDialog()
	{
		if (_dialogNode != null)
		{
			_dialogNode.Visible = false;
		}

		_isDialogOpen = false;
		_currentDialogIndex = 0;

		// Re-enable player movement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
	}

	private void UpdateDialogText()
	{
		if (!_questDialogs.TryGetValue(_currentQuest, out var dialogs) || 
			_currentDialogIndex >= dialogs.Count || _dialogLabel == null)
			return;

		_dialogLabel.Text = dialogs[_currentDialogIndex];
	}

	private void OnAnvilBuilt()
	{
		if (_currentQuest == QuestState.BuildAnvil)
		{
			_currentQuest = QuestState.BuildBridge;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Anvil quest completed!");
		}
	}

	private void OnBridgeBuilt()
	{
		if (_currentQuest == QuestState.BuildBridge)
		{
			_currentQuest = QuestState.HuntRabbit;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Bridge quest completed!");
		}
	}

	private void OnRabbitHunted()
	{
		if (_currentQuest == QuestState.HuntRabbit)
		{
			_currentQuest = QuestState.BuildCampfire;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Rabbit hunting quest completed!");
		}
	}

	private void OnCampfireBuilt()
	{
		if (_currentQuest == QuestState.BuildCampfire)
		{
			_currentQuest = QuestState.CookMeat;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Campfire quest completed!");
		}
	}

	public void OnMeatCooked()
	{
		if (_currentQuest == QuestState.CookMeat)
		{
			_currentQuest = QuestState.Complete;
			SaveQuestProgress();
			GD.Print("TutorialNPC: All quests completed!");
		}
	}

	private void SaveQuestProgress()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData != null)
		{
			// Save quest progress in game data
			resourcesManager.GameData.TutorialQuestState = (int)_currentQuest;
			resourcesManager.ForceSave();
		}
	}

	private void LoadQuestProgress()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData != null)
		{
			_currentQuest = (QuestState)resourcesManager.GameData.TutorialQuestState;
		}
	}
}
