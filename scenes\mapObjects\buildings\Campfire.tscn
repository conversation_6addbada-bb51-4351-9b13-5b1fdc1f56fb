[gd_scene load_steps=11 format=3 uid="uid://bjwf13miq7lo7"]

[ext_resource type="Texture2D" uid="uid://chqakc1gv5pov" path="res://resources/solaria/buildings/animated/campfire.png" id="1_b6qg3"]
[ext_resource type="Script" uid="uid://dto8j0ch1bgfj" path="res://scenes/mapObjects/buildings/Campfire.cs" id="1_script"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="2_27ba4"]
[ext_resource type="PackedScene" uid="uid://4pqvx3kbvmfm" path="res://scenes/UI/buildingMenus/CampfireMenu.tscn" id="3_b6qg3"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="4_u2oqx"]

[sub_resource type="Animation" id="Animation_27ba4"]
resource_name = "AnimateCampfire"
length = 0.8
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Campfire:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7]
}

[sub_resource type="Animation" id="Animation_t586u"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Campfire:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_u2oqx"]
_data = {
&"AnimateCampfire": SubResource("Animation_27ba4"),
&"RESET": SubResource("Animation_t586u")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_usjcl"]
size = Vector2(15, 9)

[sub_resource type="CircleShape2D" id="CircleShape2D_gh3ed"]

[node name="Campfire" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_u2oqx")
}
speed_scale = 0.6

[node name="Campfire" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("1_b6qg3")
hframes = 8

[node name="CraftingResource" type="Sprite2D" parent="."]
y_sort_enabled = true
scale = Vector2(0.75, 0.75)
offset = Vector2(0, -30)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 10)
shape = SubResource("RectangleShape2D_usjcl")

[node name="ProgressBar" parent="." instance=ExtResource("2_27ba4")]
position = Vector2(0, 16)
scale = Vector2(1.05, 0.6)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 7)
shape = SubResource("CircleShape2D_gh3ed")

[node name="CampfireMenu" parent="." instance=ExtResource("3_b6qg3")]

[node name="ProgressBarVertical" parent="." instance=ExtResource("4_u2oqx")]
visible = false
z_index = 1
position = Vector2(10, 4)
